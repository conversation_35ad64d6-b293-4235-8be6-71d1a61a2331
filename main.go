package main

import (
	"aiTestPool/omnichannel"
	"bufio"
	"context"
	"fmt"
	"os"
	"strings"

	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/text/gstr"
	"google.golang.org/genai"
)

var (
	ProjectID = "aile-ai-development"
	Region    = "global"
	//Model     = "gemini-2.5-pro-exp-03-25"
	Model = "gemini-2.5-flash"
	//Model = "gemini-2.0-flash"
	//Model  = "gemini-2.5-pro-preview-03-25"
	//ApiKey = "AIzaSyDXPFGhswnBhq8JC7pJBTb0vKcIFeAIrm0"
)

func main() {
	ctx := gctx.New()
	os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", "./aile-ai-development.json")

	// 設置 Vertex AI 環境變量
	//os.Setenv("GOOGLE_GENAI_USE_VERTEXAI", "true")
	//os.Setenv("GOOGLE_CLOUD_PROJECT", ProjectID)
	//os.Setenv("GOOGLE_CLOUD_LOCATION", Region)

	// 初始化 Vertex AI 客戶端
	client, err := genai.NewClient(ctx, &genai.ClientConfig{
		Project:  ProjectID,
		Location: Region,
		Backend:  genai.BackendVertexAI,
	})
	if err != nil {
		g.Log().Fatalf(ctx, "Failed to create genai client: %v", err)
	}

	g.Log().Info(ctx, "AI Chat initialized successfully")
	fmt.Println("=== AI 對話程序 (使用 Chat Session) ===")
	fmt.Printf("使用模型: %s\n", Model)
	fmt.Printf("項目ID: %s\n", ProjectID)
	fmt.Printf("區域: %s\n", Region)
	fmt.Println("輸入 'exit' 或 'quit' 退出程序")
	fmt.Println("輸入 'history' 查看對話歷史")
	fmt.Println("輸入 'tokens' 查看 Token 使用統計")
	fmt.Println("每次對話後會自動顯示 Token 使用量")
	fmt.Println("請開始對話:")

	// 創建聊天會話
	fmt.Println("正在創建聊天會話...")
	initialHistory := []*genai.Content{}
	chatSession, err := client.Chats.Create(ctx, Model, nil, initialHistory)
	if err != nil {
		g.Log().Fatalf(ctx, "Failed to create chat session: %v", err)
	}
	g.Log().Info(ctx, "Chat session created successfully")

	// 創建輸入掃描器
	scanner := bufio.NewScanner(os.Stdin)

	// 開始對話循環
	for {
		fmt.Print("\n您: ")
		if !scanner.Scan() {
			break
		}

		userInput := strings.TrimSpace(scanner.Text())

		// 檢查退出命令
		if userInput == "exit" || userInput == "quit" {
			fmt.Println("再見！")
			break
		}

		// 檢查歷史命令
		if userInput == "history" {
			fmt.Println("\n=== 對話歷史 (來自 chatSession.History) ===")
			history := chatSession.History(false) // false 表示不包含系統消息
			for i, content := range history {
				role := content.Role
				if len(content.Parts) > 0 && content.Parts[0].Text != "" {
					fmt.Printf("%d. %s: %s\n", i+1, role, content.Parts[0].Text)
				}
			}
			fmt.Println("=== 歷史結束 ===")
			continue
		}

		// 檢查 token 命令
		if userInput == "tokens" {
			fmt.Println("\n=== Token 使用統計 ===")
			history := chatSession.History(false)
			if len(history) > 0 {
				// 計算當前對話歷史的 token 數量
				tokenResp, err := client.Models.CountTokens(ctx, Model, history, nil)
				if err != nil {
					fmt.Printf("計算 token 失敗: %v\n", err)
				} else {
					fmt.Printf("總 Token 數量: %d\n", tokenResp.TotalTokens)
				}
			} else {
				fmt.Println("目前沒有對話歷史")
			}
			fmt.Println("=== Token 統計結束 ===")
			continue
		}

		if userInput == "" {
			continue
		}

		// 構建用戶消息
		userParts := []*genai.Part{
			{Text: userInput},
		}

		// 發送消息到聊天會話
		g.Log().Infof(ctx, "Sending message to chat session")
		resp, err := chatSession.SendMessage(ctx, *userParts[0])
		if err != nil {
			g.Log().Errorf(ctx, "Failed to send message: %v", err)
			fmt.Printf("錯誤: 無法發送消息 - %v\n", err)
			continue
		}
		g.Log().Infof(ctx, "Received response from chat session")

		// 顯示 AI 回應
		if resp != nil && len(resp.Candidates) > 0 && len(resp.Candidates[0].Content.Parts) > 0 {
			if textPart := resp.Candidates[0].Content.Parts[0].Text; textPart != "" {
				fmt.Printf("\nAI: %s\n", textPart)
			} else {
				fmt.Println("\nAI: 抱歉，我無法生成回應。")
			}
		} else {
			fmt.Println("\nAI: 抱歉，我無法生成回應。")
		}

		// 計算並顯示當前對話的 token 使用量
		fmt.Println("\n--- Token 使用統計 ---")
		history := chatSession.History(false)
		if len(history) > 0 {
			tokenResp, err := client.Models.CountTokens(ctx, Model, history, nil)
			if err != nil {
				fmt.Printf("計算 token 失敗: %v\n", err)
			} else {
				fmt.Printf("當前對話總 Token 數量: %d\n", tokenResp.TotalTokens)
				fmt.Printf("對話輪次: %d\n", len(history)/2) // 假設每輪對話包含用戶和AI各一條消息
			}
		}
		fmt.Println("--- Token 統計結束 ---")
	}
}

func CreateAnswer(ctx context.Context, in g.MapStrStr) (answer []*omnichannel.Root, err error) {
	if in == nil {
		err = gerror.New("input answer is nil")
		panic(err)
	}
	answer = make([]*omnichannel.Root, 0)

	mapAnswers := gmap.NewStrStrMapFrom(in)
	answerText := gstr.Join(mapAnswers.Keys(), `\n`)
	// 每一個 value ： <ele ^^ ele>
	quickReply := gstr.SplitAndTrim(gstr.Join(mapAnswers.Values(), "^^"), "^^")

	var ans = &omnichannel.Root{
		Text: answerText,
		Type: "Text",
	}

	fnCreateItem := func(qrStr string) *omnichannel.Item {
		return &omnichannel.Item{
			Action: &omnichannel.Action{
				Data:        qrStr,
				DisplayText: qrStr,
				Title:       qrStr,
				Type:        "Postback",
			},
			Type: "Action",
		}
	}

	if len(quickReply) > 0 {
		var items = make([]*omnichannel.Item, 0)
		for _, qrStr := range quickReply {
			items = append(items, fnCreateItem(qrStr))
		}

		ans.QuickReply = &omnichannel.QuickReply{
			Items: items,
		}
	}

	answer = append(answer, ans)
	return

}
