package main

import (
	"aiTestPool/model"
	"cloud.google.com/go/storage"
	"fmt"
	"github.com/go-openapi/strfmt"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/container/gpool"
	"github.com/gogf/gf/v2/encoding/gbase64"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gproc"
	"github.com/gogf/gf/v2/os/grpool"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/kkdai/youtube/v2"
	"golang.org/x/net/context"
	"io"
	"math/rand"
	"os"
	"path"
	"testing"
	"time"
)

func TestConfig(t *testing.T) {
	ctx := gctx.GetInitCtx()
	vcfg, err := g.Cfg().Get(ctx, "llms")
	if err != nil {
		t.Error(err)
	}
	var cfg *model.LLMsConfig
	_ = vcfg.Struct(&cfg)
	fmt.Println(gjson.New(cfg).MustToJsonIndentString())

}

func TestIntefaceConvert(t *testing.T) {
	pool := gpool.New(3*time.Second, func() (interface{}, error) {
		return &MyLLM{}, nil
	}, func(i interface{}) {
		fmt.Println("expired ...")
	})
	v, err := pool.Get()
	if err != nil {
		panic(err)
	}

	v.(ILlm).DoSome()

}

type ILlm interface {
	DoSome()
}
type MyLLM struct {
}

func (m *MyLLM) DoSome() {
	fmt.Println("myLLM call dosome ... ")
}

type MessageContent struct {
	TimeStamp int64  `json:"time_stamp"`
	Message   string `json:"message"`
}

func TestInternalStruct(t *testing.T) {
	type msg struct {
		DateTime string `json:"date_time"`
		Message  string `json:"message"`
	}
	ary := garray.NewArray()
	for i := 0; i < 2; i++ {
		ary.Append(msg{
			DateTime: gtime.Now().Format("H:i:s"),
			Message:  fmt.Sprintf("message %v", i),
		})

	}
	fmt.Println(ary.Len())
	fmt.Println(gjson.New(ary.Slice()).MustToJsonIndentString())
}
func TestEncode(t *testing.T) {
	var s = ` 
 {  
   "company": "捷報人力資源服務集團",
   "name": "陳麒元", 
   "occupation" :  "總經理室 特別助理",
  "phone": "0933-162-833",  
  "fax": "", 
  "eMail": "<EMAIL>", 
  "address": "桃園市桃園區中正路1071號6樓之3" 
} 
 `
	json := gjson.New(s)
	t.Log(json.MustToJsonIndentString())
}

func TestProcStr(t *testing.T) {
	var str = "你好！我主要可以協助您進行 **名片識別** 和 **建立工時日誌**。\\n\\n請問您需要哪方面的協助呢？ 請提供圖片或相關資訊（例如：時間、地點、事件）。"
	str = gstr.TrimAll(str)

	str = gstr.SubStrFrom(str, "```json")
	str = gstr.TrimLeftStr(str, "```json\n")
	str = gstr.TrimLeftStr(str, "```json")
	str = gstr.TrimRightStr(str, "\n```")
	str = gstr.TrimRightStr(str, "```")
	println(str)
}
func TestStr2(t *testing.T) {
	m := gmap.NewStrStrMapFrom(g.MapStrStr{"a1": "", "a2": "v4^^v5^^v6"})
	v := gstr.Join(m.Values(), "^^")
	fmt.Println(gstr.Trim(v, "^^"))

}
func TestStr3(t *testing.T) {
	var s = `gs://a/b/c.pdf`
	g.Dump(gfile.Name(s))
}
func TestBuket(t *testing.T) {
	//var (
	//	ProjectID = "aile-ai-development"
	//)
	os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", "./key.json")
	var ctx = gctx.New()
	client, err := storage.NewGRPCClient(ctx)
	if err != nil {
		panic(err)
	}
	defer client.Close()

	bucket := client.Bucket("dev_store123")

	fileUrl := "gs://dev_store123/16172383794166241269/sample_0.mp4"
	obj := bucket.Object(gstr.TrimLeftStr(fileUrl, "gs://"+"dev_store123"+`/`))

	if rc, e := obj.NewReader(ctx); e != nil {
		panic(e)
	} else {
		defer rc.Close()
		data, err := io.ReadAll(rc)
		if err != nil {
			panic(err)
		}
		outputPath := "./files/sample.mp4"
		if err := gfile.Mkdir(gfile.Dir(outputPath)); err != nil {
			panic(err)
		}
		if err := gfile.PutBytes(outputPath, data); err != nil {
			panic(err)
		}
	}

	//attrs, err := obj.Attrs(ctx)
	//if errors.Is(err, storage.ErrObjectNotExist) {
	//	fmt.Println("Object does not exist")
	//} else if err != nil {
	//	panic(err)
	//} else {
	//
	//	fmt.Printf("Object exists with size: %d\n", attrs.Size)
	//	g.Dump(attrs)
	//}

	//var wc = obj.NewWriter(ctx)
	//f, _ := gfile.OpenFile("./files/Qing.pdf", os.O_RDONLY, gfile.DefaultPermOpen)
	//io.Copy(wc, f)
	//wc.Close()

	//err = obj.Delete(ctx)
	//if err != nil {
	//	panic(err)
	//}
	//fmt.Println("delete ok")

}

func TestFiles(t *testing.T) {
	//g.Dump(gfile.ScanDir("./", "*.*", false))

	files, _ := gfile.ScanDirFile("./", "*.*", false)
	for _, f := range files {
		stat, _ := gfile.Stat(f)
		g.Dump(stat.Size())
		g.Dump(gfile.Basename(f))
	}
	filename := gfile.Join(gfile.Temp("brainHub"), "test.txt")
	fmt.Println(filename)
	gfile.PutContents(filename, "testabc")
}

type UploadFile struct {
	FileName string `json:"file_name"`
	OnCloud  bool   `json:"on_cloud"`
}

func TestBase(t *testing.T) {
	ary := garray.NewArray()
	ary.Append(&UploadFile{
		FileName: "file1",
		OnCloud:  false,
	},
		&UploadFile{
			FileName: "file2",
			OnCloud:  false,
		},
	)

	ctx := gctx.GetInitCtx()

	_, _ = g.Redis().Set(ctx, "arraytest", ary)
	v, _ := g.Redis().Get(ctx, "arraytest")
	var uploadFileAry []*UploadFile
	v.Structs(&uploadFileAry)
	g.Dump(uploadFileAry)
}
func TestGetYTContentType(t *testing.T) {
	var url = "https://www.youtube.com/watch?v=WwuvIYuC2Xk"
	client := youtube.Client{}
	video, err := client.GetVideo(url)
	if err != nil {
		panic(err)
	}
	for _, format := range video.Formats {
		g.Dump(format.MimeType)
	}
}

func TestCreateAnswer(t *testing.T) {
	ctx := gctx.New()
	ans, err := CreateAnswer(ctx, g.MapStrStr{
		"answer text1": "QR1^^QR2",
	})

	if err != nil {
		panic(err)
	}
	fmt.Println(gjson.New(ans).MustToJsonIndentString())

}

func TestShirkMD(t *testing.T) {
	var count int64
	_ = gfile.ReadLines("./files/Qing.md", func(line string) error {
		count += gconv.Int64(gstr.LenRune(gstr.Trim(line)))
		return nil
	})

	fmt.Println(count)
}
func TestConvertTOMD(t *testing.T) {
	//ctx := gctx.GetInitCtx()

	g.Dump(gproc.SearchBinaryPath("markitdown"))
	g.Dump(gproc.SearchBinary("markitdown1"))

	//dur := gtime.FuncCost(
	//	func() {
	//
	//		pdfFile := "./files/Qing.pdf"
	//		_, err := gproc.ShellExec(ctx, fmt.Sprintf("markitdown %s  --keep-data-uris  -o  %s", pdfFile, gfile.Join(gfile.Dir(pdfFile), gfile.Name(pdfFile)+".md")))
	//		if err != nil {
	//			panic(err)
	//		}
	//	},
	//)
	//
	//t.Log(dur.String())

}

func TestDir(t *testing.T) {
	var s = `./files/qing.pdf`
	g.Dump(gfile.Dir(s))
	g.Dump(gfile.RealPath(s))
	g.Dump(gfile.ExtName(s))
	s = "https://youtu.be/0rBEMLw65Jk;https://ww.youtube.com/shorts/XovOxtnEXHc?feature=share"
	g.Dump(gstr.SplitAndTrim(s, ";"))
	g.Dump(gbase64.EncodeString("https://youtu.be/0rBEMLw65Jk"))
}

func TestMap(t *testing.T) {
	var m = make(map[string]bool)
	m["a"] = true
	g.Dump(m)
	modMap(m)
	g.Dump(m)
}
func modMap(m map[string]bool) {
	m["b"] = false
}
func TestScanDirFile(t *testing.T) {
	//g.Dump(gfile.ScanDirFile("./webpages", "*", false))
	g.Dump(gfile.Exists("./files1"))
}

func TestPool(t *testing.T) {

	pool := grpool.New()
	ctx := gctx.New()

	timeoutCtx, cancel := context.WithTimeout(ctx, 10*time.Second)

	defer cancel()

	err := pool.AddWithRecover(timeoutCtx, func(ctx context.Context) {
		for {
			select {
			case <-ctx.Done():
				glog.Print(ctx, "timeout ")
				return
			default:
				time.Sleep(time.Second)
				glog.Print(ctx, "interval...")
			}
		}

	}, func(ctx context.Context, exception error) {
		glog.Print(ctx, exception.Error())
	})
	if err != nil {
		panic(err)
	}
	select {}
}
func TestSplit(t *testing.T) {
	var tid = `20231017-0001`
	var sid = `18b3ce3b-f7d0-07f6-50c2-000c2903841d`
	var s = tid + ":" + sid
	g.Dump(gstr.SplitAndTrim(s, ":"))
}
func TestRedis(t *testing.T) {
	ctx := gctx.GetInitCtx()
	rdb := g.Redis()
	v, err := rdb.Do(ctx, "FT.DROPINDEX", "vector_idx", "DD")
	if err != nil {
		glog.Printf(ctx, "failed: %v", err)
	}
	g.Dump(v)

	v, err = rdb.Do(ctx, "FT.CREATE", "vector_idx",
		"ON", "HASH",
		"PREFIX", "1", "doc:",
		"SCHEMA",
		"vector", "VECTOR", "FLAT", "6",
		"TYPE", "FLOAT32", "DIM", "128",
		"DISTANCE_METRIC", "COSINE",
	)
	if err != nil {
		panic(err)
	}
	g.Dump(v)

	for i := 0; i < 3; i++ {
		vector := randomVector(128)
		_, err = rdb.HSet(ctx, fmt.Sprintf("doc:%d", i), g.Map{"vector": vector})
		if err != nil {
			panic(err)
		}
	}
	glog.Print(ctx, "向量數據已經添加")
	queryVector := randomVector(128)
	query := fmt.Sprintf("(*)=>[KNN 2 @vector $BLOB AS score]")
	params := g.Map{
		"BLOB": gconv.Bytes(queryVector),
	}
	res, e := rdb.Do(ctx, "FT.SEARCH", "vector_idx", query, "PARAMS", "2", "BLOB", params["BLOB"])
	if e != nil {
		glog.Print(ctx, e)
		glog.Print(ctx, "failed to query")
		return
	}
	glog.Printf(ctx, "result:%v", res)

}

// 生成随机向量
func randomVector(dim int) []float32 {
	vector := make([]float32, dim)
	for i := range vector {
		vector[i] = rand.Float32()
	}
	return vector
}

type EkbContent struct {
	PageContent string      `json:"page_content"`
	Tag         string      `json:"tag"`
	Source      string      `json:"source"`
	Question    string      `json:"question"`
	Version     float32     `json:"version"`
	Status      string      `json:"status"`
	Available   bool        `json:"available"`
	ID          strfmt.UUID `json:"-"`
	Vector      []float32   `json:"-"`
}

func TestEkbContent(t *testing.T) {

	var ekb = &EkbContent{
		PageContent: "content",
		Tag:         "tag",
		Source:      "source",
		Question:    "question",
		Version:     1.0,
		Status:      "online",
		Available:   false,
		ID:          "abcd-ef-12121",
		Vector:      []float32{1.0, 2.0},
	}
	m := gconv.Map(ekb)
	g.Dump(m)
	var s = `de3c0d08-4f05-45d3-b781-2e77144cb1c2,f48ca36e-a5fb-4c6c-84a5-63e96aa402c3,"Q:出差旅費可報銷哪4種費用？A:國內外出差時可報銷的費用：交通費、住宿費、膳雜費和特支費。交通費實報實銷，包括飛機、輪船、火車等經濟艙票價。住宿費依職等有不同標準，十一職等以上每人1,800元，兩人2,000元；十一職等以下每人1,600元，兩人1,800元。膳雜費每日500元，出差兩天以上可申請。特支費包括交際費、郵電費等特殊支出。膳雜費分配為早餐100元、午晚餐各200元。住宿須選擇合法觀光飯店，若住公司宿舍則不予支給。",online,R010,202411220021`
	g.Dump(gstr.Split(s, ","))
}
func TestSplitFile(t *testing.T) {
	gfile.ReadLines("/Users/<USER>/go examples/aiTestPool/files/abc.tsv", func(line string) error {
		data := gstr.Split(line, "\t")
		if len(data) != 6 {
			t.Log(line)
		}

		return nil
	})
}

type VData struct {
	ID   string `json:"id" `
	Name string `json:"name" v:"required"`
}

func dosome(in *VData) {

	if err := g.Validator().Data(in).Run(context.TODO()); err != nil {
		panic(err)
	}

}
func TestValidator(t *testing.T) {
	dosome(&VData{ID: "abc", Name: "wilson"})
}
func TestMapData(t *testing.T) {
	fnReCreateMap := func(m map[string]any) {
		for k, v := range m {
			if k == "_additional" {
				if additionalMap, ok := v.(map[string]any); ok {
					for ak, av := range additionalMap {
						m[ak] = av
					}
					delete(m, "_additional")
				}
			}
		}
	}
	var m = map[string]any{
		"Name": "wilson",
		"_additional": map[string]any{
			"id":     "abc-1234",
			"vector": []float32{1.0, 2.0},
		},
		"age": 12,
	}
	fnReCreateMap(m)
	g.Dump(m)
}
func TestSwitch(t *testing.T) {
	var s = "abc"
	switch s {
	default:
		t.Log("default")
	case "id", "vector":
		t.Log(s)
	}
}
func TestConvMap(t *testing.T) {
	var data = []map[string]any{
		{
			"a1": "a1",
			"a2": "a2",
		},
		{
			"b1": 1,
			"b2": 2,
		},
	}
	ret := gjson.New("data")
	_ = ret.Set("data", data)
	fmt.Println(ret.MustToJsonIndentString())

}
func TestFile(t *testing.T) {
	g.Dump(path.IsAbs("./key.json"))
}
