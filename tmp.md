# 臨時代碼
```go
 client, err := genai.NewClient(ctx, &genai.ClientConfig{
Backend:  genai.BackendVertexAI,
Project:  ProjectID,
Location: Region,
})
v, _ := g.Cfg().Get(ctx, "prompts.chat_system_instruction")
var systemInstruction = v.String()

var config = &genai.GenerateContentConfig{
Temperature:       genai.Ptr[float32](1.0),
MaxOutputTokens:   2000,
SystemInstruction: genai.NewContentFromText(systemInstruction, "user"),
ResponseMIMEType:  "application/json",
ThinkingConfig: &genai.ThinkingConfig{
IncludeThoughts: false,
//ThinkingBudget:  genai.Ptr[int32](4096),
ThinkingBudget: genai.Ptr[int32](0),
},
}
contents := make([]*genai.Content, 0)

files, _ := gfile.ScanDirFile("./webpages", "*", false)
for _, file := range files {
buf := gfile.GetBytes(file)
contents = append(contents, genai.NewContentFromBytes(
buf, "text/md", genai.RoleUser,
))
}
//config.CachedContent = catchContent.Name
chat, err := client.Chats.Create(ctx, Model, config, contents)
if err != nil {
panic(err)
}

//contents = append(contents, genai.NewContentFromURI(
//	fmt.Sprintf("gs://%s/%s", "dev_store123/T123/S123", "Qing.md"),
//	"text/md", genai.RoleUser,
//))

//contents = append(contents, genai.NewContentFromURI(
//	"https://www.youtube.com/watch?v=WwuvIYuC2Xk",
//	"video/mp4", genai.RoleUser,
//))

send := func(message string) {
var content *genai.Content
if gfile.Exists(message) {
data := gfile.GetBytes(message)
content = genai.NewContentFromBytes(data, http.DetectContentType(data), "user")

} else {
content = genai.NewContentFromText(message, "user")

}
var parts []genai.Part

for _, part := range content.Parts {
parts = append(parts, *part)
}
r, e := chat.SendMessage(ctx, parts...)

if e != nil {
g.Log().Error(ctx, e)
return
}
g.Log().Infof(ctx, "%v", gjson.New(r).MustToJsonIndentString())

strResponse := r.Candidates[0].Content.Parts[0].Text

fmt.Println("ai:", strResponse, gjson.Valid(strResponse))

//fmt.Println(gjson.New(chat.History(false)).MustToJsonIndentString())

}
for {
userInput := gstr.TrimRightStr(gcmd.Scan("user:"), "/n")
if len(chat.History(false)) > 0 {
cntResp, err := chat.CountTokens(ctx, Model, chat.History(false), nil)
if err != nil {
g.Log().Error(ctx, err)
} else {
g.Log().Infof(ctx, "cntResp:%v", gjson.New(cntResp).MustToJsonIndentString())
}

}
if userInput == "q" {
return
}
dur := gtime.FuncCost(func() {

send(userInput)
})
fmt.Println(dur.String())
}
```