package model

// Config 表示顶层配置结构
type Config struct {
	LLMs LLMsConfig `json:"llms"`
}

// LLMsConfig 包含所有LLM相关的配置
type LLMsConfig struct {
	Common CommonConfig `json:"common"`
	Vertex VertexConfig `json:"vertex"`
}

// CommonConfig 包含通用的LLM设置
type CommonConfig struct {
	Temperature     float64 `json:"temperature"`
	MaxOutputTokens int     `json:"max_output_tokens"`
}

// VertexConfig 包含Vertex AI特定的配置
type VertexConfig struct {
	ProjectID      string         `json:"project_id"`
	Region         string         `json:"region"`
	CredentialFile string         `json:"credential_file"`
	Gemini         GeminiConfig   `json:"gemini"`
	AnthropicModel AnthropicModel `json:"anthropic_model"`
}

type AnthropicModel struct {
	Model string `json:"model"`
}

func (c *LLMsConfig) GetVertexEndPoint() string {
	return "https://" + c.Vertex.Region + "-aiplatform.googleapis.com/v1/projects/" + c.Vertex.ProjectID + "/locations/" + c.Vertex.Region
}

// GeminiConfig 包含Gemini模型的特定配置
type GeminiConfig struct {
	Model           string  `json:"model"`
	Temperature     float64 `json:"temperature"`
	MaxOutputTokens int     `json:"max_output_tokens"`
}
